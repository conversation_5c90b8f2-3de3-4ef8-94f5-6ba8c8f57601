import { Dimensions, StyleSheet, View } from 'react-native'
import React from 'react'
import { Bar<PERSON><PERSON> } from 'react-native-gifted-charts'
import { useTheme } from '../../../context/ThemeContext'
import { AppFonts } from '../../../constants/theme/fonts/fonts'

const MonthlyTabletGraph = () => {
  const { theme } = useTheme()

  // Sample data matching the UI design with gradients
  const barData = [
    {
      value: 6,
      label: 'Mon',
      frontColor: '#9CA3AF66', // Gray color (100% opacity)
      gradientColor: '#9CA3AF', // Gray color (20% opacity)
    },
    {
      value: 7,
      label: 'Tue',
      frontColor: '#9CA3AF66', // Gray color (100% opacity)
      gradientColor: '#9CA3AF', // Gray color (20% opacity)
    },
    {
      value: 5,
      label: 'Wed',
      frontColor: '#F9731666', // Orange color (100% opacity)
      gradientColor: '#F97316', // Orange color (20% opacity)
    },
    {
      value: 8,
      label: 'Thurs',
      frontColor: '#84CC1666', // Green color (100% opacity)
      gradientColor: '#84CC16', // Green color (20% opacity)
    },
    {
      value: 4,
      label: 'Fri',
      frontColor: '#9CA3AF66', // Gray color (100% opacity)
      gradientColor: '#9CA3AF', // Gray color (20% opacity)
    },
    {
      value: 6,
      label: 'Sat',
      frontColor: '#9CA3AF66', // Gray color (100% opacity)
      gradientColor: '#9CA3AF', // Gray color (20% opacity)
    },
    {
      value: 6,
      label: 'Sun',
      frontColor: '#9CA3AF66', // Gray color (100% opacity)
      gradientColor: '#9CA3AF', // Gray color (20% opacity)
      spacing:0
    },
  ]

  return (
    <View style={styles.container}>
      <BarChart
        data={barData}
        width={Dimensions.get('window').width-88}
        height={160}
        barWidth={(Dimensions.get('window').width-92)/(barData.length*2)}
        spacing={(Dimensions.get('window').width-92)/((barData.length-1)*2)}
        roundedTop={false}
        roundedBottom={false}
        hideRules={true}
        // hideYAxisText={true}
        xAxisThickness={0}
        yAxisThickness={-10}
        yAxisTextStyle={{
          color: theme.text.primary,
          fontFamily:AppFonts.HelixaBold,
          fontSize: 10,
        }}
        xAxisLabelTextStyle={{
          color: theme.text.primary,
          fontFamily:AppFonts.HelixaBold,
          fontSize: 10,
          textAlign: 'center',
        }}
        noOfSections={4}
        maxValue={8}
        stepValue={2}
        initialSpacing={4}
        barBorderRadius={4}
        adjustToWidth={true}
        endSpacing={4}
        rulesColor={theme.text.secondary}
        rulesType="solid"
        showReferenceLine1={false}
        showReferenceLine2={false}
        showReferenceLine3={false}
        backgroundColor="transparent"
        isAnimated
        disableScroll={true}
        showGradient={true}
      />
    </View>
  )
}

export default MonthlyTabletGraph

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginLeft:-24
  },
})